/* Modern Features Section - Enhanced Styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Work Sans', sans-serif;
}

.features {
  background: #f0f4f8;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(0, 18, 51, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 123, 255, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 2px 2px, rgba(0, 18, 51, 0.08) 1px, transparent 0);
  background-size: 60px 60px;
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.feature-container {
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.grid-container {
  display: grid;
  grid-template-columns: 35% 65%;
  align-items: center;
  gap: 60px;
  max-width: 1400px;
  margin: 0 auto;
}

.text-content {
  padding-right: 2rem;
}

.text-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #001233 0%, #2c5aa0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  position: relative;
}

.text-content h2::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #001233, #007bff);
  border-radius: 2px;
}

.text-content p {
  color: #4a5568;
  line-height: 1.7;
  font-size: 1.1rem;
  margin-top: 30px;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  padding: 35px 30px;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 18, 51, 0.08),
    0 8px 16px rgba(0, 18, 51, 0.04);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #001233, #007bff);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 30px 60px rgba(0, 18, 51, 0.15),
    0 12px 24px rgba(0, 18, 51, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.card-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #001233 0%, #2c5aa0 100%);
  border-radius: 12px;
  color: white;
  transition: all 0.3s ease;
}

.card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 20px rgba(0, 18, 51, 0.3);
}

.card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.card p {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.6;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 18, 51, 0.02), rgba(0, 123, 255, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover .card-overlay {
  opacity: 1;
}

.custom-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.custom-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  gap: 12px;
  font-size: 1rem;
  color: #4a5568;
  line-height: 1.5;
}

.custom-list svg {
  width: 20px;
  height: 20px;
  color: #28a745;
  flex-shrink: 0;
  margin-top: 2px;
}

.custom-list span {
  line-height: 1.4;
  font-weight: 500;
}

.animate-text {
  opacity: 0;
  animation: 1s forwards fadeInUp;
}

.animate-text-delay {
  opacity: 0;
  animation: 1s 0.3s forwards fadeInUp;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .grid-container {
    grid-template-columns: 40% 60%;
    gap: 40px;
  }

  .text-content h2 {
    font-size: 2.4rem;
  }
}

@media (max-width: 1024px) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .text-content {
    padding-right: 0;
  }

  .text-content h2::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .features {
    padding: 80px 0;
  }

  .feature-container {
    padding: 0 15px;
  }

  .text-content h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .cards-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card {
    padding: 30px 25px;
    border-radius: 15px;
  }

  .card-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
  }

  .card h3 {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .features {
    padding: 60px 0;
  }

  .feature-container {
    padding: 0 10px;
  }

  .text-content h2 {
    font-size: 1.8rem;
  }

  .text-content p {
    font-size: 1rem;
  }

  .card {
    padding: 25px 20px;
    border-radius: 12px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .card h3 {
    font-size: 1.1rem;
  }

  .custom-list li {
    margin-bottom: 12px;
    font-size: 0.95rem;
  }

  .custom-list svg {
    width: 18px;
    height: 18px;
  }
}