

body,
html {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: Roboto, sans-serif;
    box-sizing: border-box;
    overflow-x: hidden;
    scroll-behavior: smooth;
    color: #4b3621
}

:root {
    --primary-deep-green: #04300e;
    --primary-sunny-yellow: #c2ac2c;
    --primary-white: #FFFFFF;
    --primary-deep-brown: #4B3621
}

.hero-section {
    position: relative;
    width: 100%;
    height: 80vh;
    overflow: hidden;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Dark overlay */
    z-index: 1; /* Behind content but above the background */
  }

  iframe {
    width: 100%;
  }

  /* Slider Setup */
  .slider {
    position: relative;
    height: 100%;
    display: flex;
    z-index: 2; /* Ensure slider is above the overlay */
  }

  .slide {
    min-width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: none;
    justify-content: center;
    align-items: center;
  }

  .slide.active {
    display: flex;
  }

  /* Content Alignment */
  .content {
    position: absolute;
    bottom: 20%;
    left: 10%;
    color: #fdcd1b;
    text-align: left;
    max-width: 500px;
    z-index: 3; /* Content above overlay */
  }

  h1 {
    font-size: 3rem;
    margin-bottom: 10px;
  }

  h2 {
    font-size: 1.5rem;
  }

  p {
    margin-top: 10px;
    margin-bottom: 20px;
    font-size: 1.1rem;
  }

  .buttons .btn {
    margin-right: 10px;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 1rem;
    display: inline-block;
  }

  .btn-green {
    background-color: #064815d8;
    color: #fff;
  }

  .btn-white {
    background-color: #fff;
    color: #000;
  }

  /* Arrow Controls */
  .prev, .next {
    position: absolute;
    top: 50%;
    font-size: 30px;
    color: #fdcd1b;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px;
    cursor: pointer;
    border-radius: 50%;
    transform: translateY(-50%);
    user-select: none;
    z-index: 3; /* Ensure arrows are above overlay */
  }

  .prev {
    left: 10px;
  }

  .next {
    right: 10px;
  }

  /* Responsive Media Queries */
  @media (max-width: 768px) {
    .content {
      left: 5%;
      bottom: 15%;
    }

    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.2rem;
    }

    p {
      font-size: 1rem;
    }

    .buttons .btn {
      font-size: 0.9rem;
      padding: 8px 16px;
    }
  }


  .bowl-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background: #f0f8ff;
    position: relative;
  }

  .bowl-heading {
    position: absolute;
    top: -2rem; /* Adjust based on heading size */
    font-size: 2.5rem;
    background: #ffffff;
    padding: 0 1rem;
    z-index: 1;
  }

  .bowl-body {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    max-width: 1200px;
    background: #ffffff;
    border-radius: 16% 50% 0 0;
    padding: 2rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 0;
  }

  .about-content {
    flex: 1;
    padding-right: 1rem;
  }

  .about-image img {
    width: 100%;
    height: auto;
    border-radius: 0 1800px 20px 0; /* Rounded top-right corner */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .why-us {
    width: 100%;
    max-width: 1200px;
    background: #ffffff;
    border-radius: 0 0 50% 50%;
    text-align: center;
    padding: 1rem;
    margin-top: -1rem; /* Adjust overlap */
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.1);
  }

  .why-us h3 {
    margin-bottom: 1rem;
  }

  .why-us ul {
    list-style-type: none;
    padding: 0;
  }

  .why-us li {
    margin: 0.5rem 0;
  }

  @media (max-width: 768px) {
    .bowl-heading {
      font-size: 2rem;
      top: -1.5rem;
    }

    .bowl-body {
      flex-direction: column;
      align-items: center;
    }

    .about-content {
      padding-right: 0;
      text-align: center;
    }

    .about-image img {
      max-width: 80%;
    }

    .why-us {
      padding: 1rem;
      margin-top: 0;
    }
  }







.nav-link:hover,
h1 {
    color:   #285232;
}



.about-us h2,
.card-title,
.contact-section h2
 {
    text-align: center
}



.service-title,
h3 {
    text-transform: uppercase;
    color: #c2ac2c;
}

.about-us {
    background-color: var(--primary-white)
}

.para1 {
    font-size: 16px;
    line-height: 15px
}

.about-us h2,
.services-heading {
    font-size: 2rem;
    font-weight: 700
}

.about-us h2 {
    color: #c2ac2c;
}

.about-us .lead,
.about-us p {
    color: var(--primary-deep-brown);
    margin: 0 auto
}

.about-us img {
    max-width: 100%;
    height: auto;
    border-radius: .5rem
}

.services .service-card {
    border-radius: none;
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.services .service-card img {
    width: 100%;
    height: auto
}

.services .service-content {
    background-color: inherit;
    text-align: center
}

.services .service-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem
}

.services .service-description {
    font-size: 1rem;
    margin-bottom: 1rem;
    color:var(--primary-white) !important;


}

.services-heading {
    color: var(--primary-deep-green)
}

.card-title,
.package-item {
    color: var(--primary-white)
}

.bg-deep-brown {
    background-color: var(--primary-deep-brown)
}


.our-packages {
    background-color: var(--primary-sunny-yellow)
}






.products {
    padding: 20px;
    background-color: #f9f9f9;
}

.product-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    justify-items: center;
}

.product-card {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    width: 100%;
    max-width: 300px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.product-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

h3 {
    margin: 10px 0;
    font-size: 1.5em;
    color: #333;
}

p {
    color: #666;
    font-size: 1em;
}


.contact-section {
    padding: 10px 0
}

.contact-section .contact-info i {
    font-size: 1.5em;
    color: #007bff;
    margin-right: 10px
}

.contact-section .contact-info {
    margin-bottom: 20px;
    text-align: left
}

.contact-section .map-embed {
    height: 300px;
    width: 100%;
    border: none
}

.contact-section h2 {
    margin-bottom: 10px
}

/* Conversion Optimization CSS (migrated from index.html) */
.pulse-btn {
	animation: pulse 2s infinite;
	box-shadow: 0 0 20px rgba(253, 205, 27, 0.6) !important;
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.05); }
	100% { transform: scale(1); }
}

.floating-cta {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 1000;
	animation: bounce 2s infinite;
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
	40% { transform: translateY(-10px); }
	60% { transform: translateY(-5px); }
}

.urgency-timer {
	background: linear-gradient(45deg, #ff6b6b, #ee5a24);
	color: white;
	padding: 10px 20px;
	border-radius: 25px;
	display: inline-block;
	font-weight: bold;
	animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
	from { box-shadow: 0 0 10px #ff6b6b; }
	to { box-shadow: 0 0 20px #ff6b6b, 0 0 30px #ff6b6b; }
}

@media (max-width: 768px) {
	.floating-cta {
		bottom: 10px;
		right: 10px;
	}
}

/* Utility classes to replace common inline styles */
.text-black { color: #000000 !important; }
.text-white-force { color: #ffffff !important; }
.text-yellow { color: #fdcd1b !important; }
.text-alice { color: #ddf0fc !important; }
.text-alice-blue { color: #f0f8ff !important; }

.bg-light-blue { background-color: #ddf0fc !important; }

.hr-yellow {
	border: none;
	height: 1px;
	background-color: #fdcd1b;
	margin: 10px 0;
}

.link-dark-underline { color: #000000; text-decoration: underline; }
.link-white-no-underline { color: #ffffff; text-decoration: none; }

/* Service card layout extracted from inline styles */
.service-card { min-height: 450px; display: flex; flex-direction: column; }
.service-content { flex-grow: 1; }
.service-card img.img-fluid { max-height: 200px; width: 100%; object-fit: cover; }

.border-0 { border: 0 !important; }
.mt-20 { margin-top: 20px !important; }

/* Navbar inline styles moved from HTML */
.navbar-custom {
	overflow-x: hidden;
	background-color: #fdcd1b;
	width: 100%;
}

/* Slider background images moved from HTML */
.slide-1 {
	background-image: url('../img/lithium-batteries.webp');
}
.slide-2 {
	background-image: url('../img/flipped-img2.webp');
}
.slide-3 {
	background-image: url('../img/flip.webp');
}

/* Replace button inline hover handlers */
.btn-cta-black {
	background: #000;
	color: #fff;
	border: none;
	border-radius: 8px;
	font-weight: bold;
	transition: background 0.3s ease, transform 0.2s ease;
}
.btn-cta-black:hover {
	background: #333;
}

/* Social proof CTA hover effect */
.btn-social {
	transition: transform 0.3s ease;
	display: inline-block;
}
.btn-social:hover {
	transform: scale(1.05);
}

/* Footer social icons hover colors */
.social-icon { transition: color 0.3s ease; }
.social-facebook:hover { color: #3b5998 !important; }
.social-instagram:hover { color: #e4405f !important; }
.social-tiktok:hover { color: #ff0050 !important; }
.social-youtube:hover { color: #ff0000 !important; }
.social-website:hover { color: #ffffff !important; }

