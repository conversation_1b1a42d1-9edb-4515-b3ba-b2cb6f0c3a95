/* Client Reviews Section - Modern & Clean Design */
.client-reviews {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.client-reviews::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(40, 167, 69, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.client-reviews .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 70px;
  position: relative;
  z-index: 2;
}

.client-reviews .section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #28a745);
  border-radius: 2px;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 35px;
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.review-card {
  background: #ffffff;
  padding: 40px 35px;
  border-radius: 20px;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--card-color, #007bff), var(--card-color-light, #66b3ff));
  transition: height 0.3s ease;
}

.review-card:nth-child(1) {
  --card-color: #007bff;
  --card-color-light: #66b3ff;
}

.review-card:nth-child(2) {
  --card-color: #28a745;
  --card-color-light: #71dd8a;
}

.review-card:nth-child(3) {
  --card-color: #ffc107;
  --card-color-light: #ffda6a;
}

.review-card:nth-child(4) {
  --card-color: #dc3545;
  --card-color-light: #f1959b;
}

.review-card:nth-child(5) {
  --card-color: #6f42c1;
  --card-color-light: #a98eda;
}

.review-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.12),
    0 8px 16px rgba(0, 0, 0, 0.08);
}

.review-card:hover::before {
  height: 8px;
}

.review-content {
  margin-bottom: 30px;
  position: relative;
}

.review-content::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: -5px;
  font-size: 4rem;
  color: var(--card-color, #007bff);
  opacity: 0.2;
  font-family: Georgia, serif;
  line-height: 1;
}

.review-content p {
  font-size: 1.1rem;
  color: #4a5568;
  line-height: 1.7;
  margin: 0;
  font-style: italic;
  position: relative;
  z-index: 1;
  padding-left: 20px;
}

.review-author {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: var(--card-color, #007bff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.review-card:hover .author-avatar {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.author-info p {
  margin: 0;
  font-weight: 600;
  color: #2d3748;
  font-size: 1.1rem;
  font-style: normal !important;
  padding-left: 0 !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .reviews-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .client-reviews {
    padding: 80px 0;
  }
  
  .client-reviews .section-title {
    font-size: 2rem;
    margin-bottom: 50px;
  }
  
  .reviews-grid {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 0 15px;
  }
  
  .review-card {
    padding: 30px 25px;
    border-radius: 15px;
  }
  
  .review-content p {
    font-size: 1rem;
    padding-left: 15px;
  }
  
  .author-avatar {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
  
  .author-info p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .client-reviews {
    padding: 60px 0;
  }
  
  .client-reviews .section-title {
    font-size: 1.8rem;
    margin-bottom: 40px;
  }
  
  .reviews-grid {
    padding: 0 10px;
    gap: 20px;
  }
  
  .review-card {
    padding: 25px 20px;
    border-radius: 12px;
  }
  
  .review-content::before {
    font-size: 3rem;
    top: -8px;
  }
  
  .review-content p {
    font-size: 0.95rem;
    padding-left: 12px;
  }
  
  .author-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .author-info p {
    font-size: 0.95rem;
  }
}

/* Animation for cards appearing on scroll */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.review-card {
  animation: fadeInUp 0.6s ease-out;
}

.review-card:nth-child(1) { animation-delay: 0.1s; }
.review-card:nth-child(2) { animation-delay: 0.2s; }
.review-card:nth-child(3) { animation-delay: 0.3s; }
.review-card:nth-child(4) { animation-delay: 0.4s; }
.review-card:nth-child(5) { animation-delay: 0.5s; }
