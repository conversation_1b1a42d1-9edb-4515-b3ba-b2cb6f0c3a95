// Mobile menu toggle
document.querySelector('.menu-toggle').addEventListener('click', function() {
    document.querySelector('nav').classList.toggle('active');
});

// Close menu when menu item is clicked (for mobile view)
document.querySelectorAll('nav a').forEach(function(menuLink) {
    menuLink.addEventListener('click', function() {
        document.querySelector('nav').classList.remove('active');
    });
});

// Sticky header behavior with smooth transition
let lastScrollTop = 0;
const header = document.querySelector('.header-wrapper');
const scrollThreshold = 100; // Adjust this value based on when you want the header to change

// Set initial transition timing
header.style.transition = 'all 0.3s ease';

// Function to handle scroll events with throttling for better performance
function handleScroll() {
    let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (scrollTop > scrollThreshold) {
        if (!header.classList.contains('sticky')) {
            requestAnimationFrame(() => {
                header.classList.add('sticky');
            });
        }
    } else {
        if (header.classList.contains('sticky')) {
            requestAnimationFrame(() => {
                header.classList.remove('sticky');
            });
        }
    }
    lastScrollTop = scrollTop;
}

// Throttle scroll events for better performance
let scrollTimeout;
window.addEventListener('scroll', function() {
    if (!scrollTimeout) {
        scrollTimeout = setTimeout(function() {
            handleScroll();
            scrollTimeout = null;
        }, 10); // Small timeout for smooth performance
    }
});

// Initial check in case page is loaded scrolled down
document.addEventListener('DOMContentLoaded', function() {
    handleScroll();
});
