<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Elida Industries</title>
  <meta name="description"
    content="Top-grade drip systems, motorcycle parts, and more, Elida is known for reliable products that support farming and mobility across East Africa.">


  <meta name="keywords" content="Elida Industries">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-08-25">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->

      <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ">

   
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://elidaindustries.yellowpageskenya.com">
  <meta property="og:title" content="Elida Industries">
  <meta property="og:description"
    content="Top-grade drip systems, motorcycle parts, and more, Elida is known for reliable products that support farming and mobility across East Africa.">
  <meta property="og:image" content="https://elidaindustries.yellowpageskenya.com/img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://elidaindustries.yellowpageskenya.com ">
  <meta property="twitter:title" content="Elida Industries ">
  <meta property="twitter:description"
    content="Top-grade drip systems, motorcycle parts, and more, Elida is known for reliable products that support farming and mobility across East Africa.">
  <meta property="twitter:image" content="https://elidaindustries.yellowpageskenya.com/img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://elidaindustries.yellowpageskenya.com">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://elidaindustries.yellowpageskenya.com">

  <!-- <link rel="alternate" hreflang="en" href="https://elidaindustries.yellowpageskenya.com/"> -->
  <link rel="alternate" hreflang="x-default" href="https://elidaindustries.yellowpageskenya.com/">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://elidaindustries.yellowpageskenya.com/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>

 <link rel="preload" as="image" href="./img/slider_1a_scale,w_557.webp" fetchpriority="high">
   <!-- <link rel="preload" as="image" href="./img/slider_1a_scale,w_807.webp" fetchpriority="high">
    <link rel="preload" as="image" href="./img/slider_1a_scale,w_1400.webp" fetchpriority="high"> -->
     <link rel="preload" as="image" href="./img/slider_4_scale,w_536.webp" fetchpriority="high">
      <!-- <link rel="preload" as="image" href="./img/slider_4_scale,w_913.webp" fetchpriority="high">
       <link rel="preload" as="image" href="./img/slider_4_scale,w_1400.webp" fetchpriority="high"> -->
       <link rel="preload" as="image" href="./img/slider_3_scale,w_680.webp" fetchpriority="high">
      <!-- <link rel="preload" as="image" href="./img/slider_3_scale,w_996.webp" fetchpriority="high">
       <link rel="preload" as="image" href="./img/slider_3_scale,w_1400.webp" fetchpriority="high"> -->
       

  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <link rel="stylesheet" href="css/contact.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/float.css">
   <link rel="stylesheet" href="css/cta.css">
 
  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    /* body {
      font-family: 'Work Sans', sans-serif;
    } */

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

    <!-- Google tag (gtag.js) -->
    <script defer src="https://www.googletagmanager.com/gtag/js?id=G-49Y39LY25S"></script>
    <script>   window.dataLayer = window.dataLayer || []; function gtag() { dataLayer.push(arguments); } gtag('js', new Date()); gtag('config', 'G-49Y39LY25S'); </script>

    
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "https://elidaindustries.yellowpageskenya.com/#organization",
      "name": "Elida Industries Limited",
      "alternateName": "Elida E.A Industries",
      "url": "https://elidaindustries.yellowpageskenya.com",
      "logo": "https://elidaindustries.yellowpageskenya.com/img/logo.webp",
      "description": "Top-grade drip systems, motorcycle parts, and more, Elida is known for reliable products that support farming and mobility across East Africa.",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+254716950588",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Along Old Mombasa Road, Near Cabanas",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "sameAs": [
        "https://www.instagram.com/elidaindustries/",
        "https://www.linkedin.com/company/elida-east-africa-industries-limited/",
        "https://api.whatsapp.com/send/?phone=254716950588"
      ]
    },
    {
      "@type": "LocalBusiness",
      "@id": "https://elidaindustries.yellowpageskenya.com/#localbusiness",
      "name": "Elida Industries Limited",
      "image": "https://elidaindustries.yellowpageskenya.com/img/logo.webp",
      "url": "https://elidaindustries.yellowpageskenya.com",
      "telephone": "+254716950588",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Along Old Mombasa Road, Near Cabanas, Next to Solargen, Excel Chemicals, Directly Opposite Kenya Sweets",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "openingHours": "Mo-Fr 08:00-17:00",
      "priceRange": "$$",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-1.333444",
        "longitude": "36.882468"
      },
      "branchOf": {
        "@type": "Organization",
        "@id": "https://elidaindustries.yellowpageskenya.com/#organization"
      }
    },
    {
      "@type": "LocalBusiness",
      "name": "Elida Industries Shop",
      "description": "Shop location for Elida Industries products",
      "image": "https://elidaindustries.yellowpageskenya.com/img/logo.webp",
      "url": "https://elidaindustries.yellowpageskenya.com",
      "telephone": "+254716950588",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Good Hope Building, Junction of Luthuli Avenue and River Road",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "openingHours": "Mo-Fr 08:00-17:00",
      "priceRange": "$$",
      "parentOrganization": {
        "@type": "Organization",
        "@id": "https://elidaindustries.yellowpageskenya.com/#organization"
      }
    },
    {
      "@type": "WebSite",
      "@id": "https://elidaindustries.yellowpageskenya.com/#website",
      "url": "https://elidaindustries.yellowpageskenya.com",
      "name": "Elida Industries",
      "description": "Wholesale Motorcycle, Farm & Building Parts",
      "publisher": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#organization"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://elidaindustries.yellowpageskenya.com/?s={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@type": "WebPage",
      "@id": "https://elidaindustries.yellowpageskenya.com/#webpage",
      "url": "https://elidaindustries.yellowpageskenya.com",
      "name": "Elida Industries: Wholesale Motorcycle, Farm & Building Parts",
      "isPartOf": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#website"
      },
      "about": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#organization"
      },
      "datePublished": "2025-08-25",
      "description": "Top-grade drip systems, motorcycle parts, and more, Elida is known for reliable products that support farming and mobility across East Africa.",
      "inLanguage": "en",
      "potentialAction": {
        "@type": "ReadAction",
        "target": [
          "https://elidaindustries.yellowpageskenya.com"
        ]
      }
    },
    {
      "@type": "Product",
      "name": "Irrigation Products",
      "image": "https://elidaindustries.yellowpageskenya.com/img/Irrigation_Products3.webp",
      "description": "Provides connectors, take-offs, end caps, rubbers, and drippers for drip and overhead irrigation. Easy to assemble and designed to deliver precise water flow. Great for farms, gardens, and greenhouse systems.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Agricultural Supplies",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "PVC Waste & Drainage Systems",
      "image": "https://elidaindustries.yellowpageskenya.com/img/PVC_Waste_Drainage_Systems2.webp",
      "description": "Offers floor traps, bends, inspection tees, reducers, and plugs for effective waste and drainage management. Made from high-quality PVC for long-lasting performance. Suitable for both residential and commercial plumbing installations.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Plumbing Supplies",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Motorbike & Vehicle Parts",
      "image": "https://elidaindustries.yellowpageskenya.com/img/Motorbike_Vehicle_Parts.webp",
      "description": "Includes mudguards, lamp holders, visors, cowls, reflectors, and engine oils. These parts enhance safety, performance, and aesthetics. Compatible with popular motorbike and vehicle models.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Automotive Parts",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Electrical Accessories",
      "image": "https://elidaindustries.yellowpageskenya.com/img/Electrical_Accessories.webp",
      "description": "Features patress boxes, conduits, bends, couplers, cable ties, and wall plugs. Designed for safe and efficient electrical wiring. Ideal for new installations and upgrades.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Electrical Supplies",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "G.I Fittings & Valves",
      "image": "https://elidaindustries.yellowpageskenya.com/img/Gi_Fittings_Valves.webp",
      "description": "Comprises galvanized iron valves, tees, elbows, plugs, and taps for heavy-duty plumbing. Built to handle high pressure and resist corrosion. Commonly used in industrial and outdoor plumbing systems.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Plumbing Supplies",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "PPR Fittings & Pipes",
      "image": "https://elidaindustries.yellowpageskenya.com/img/PPR_Fittings_Pipes.webp",
      "description": "Includes sockets, elbows, tees, valves, and pipes for hot and cold water plumbing. These are durable, leak-proof, and ideal for both domestic and industrial use. Available in various sizes and designs to suit all plumbing needs.",
      "brand": {
        "@type": "Brand",
        "name": "Elida Industries"
      },
      "category": "Plumbing Supplies",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "AboutPage",
      "@id": "https://elidaindustries.yellowpageskenya.com/#about",
      "url": "https://elidaindustries.yellowpageskenya.com/#about",
      "name": "About Elida Industries",
      "description": "At Elida E.A Industries, we are committed to keeping you growing, moving, and building. We specialize in the manufacturing of high-quality plastic irrigation systems, motorcycle parts, and construction materials across East Africa.",
      "isPartOf": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "Organization",
        "name": "Elida Industries Limited",
        "description": "Manufacturer of high-quality plastic irrigation systems, motorcycle parts, and construction materials across East Africa.",
        "foundingDate": "2025-08-25",
        "mission": "Committed to supplying high-quality materials that support farming, transport, and construction development.",
        "vision": "To be the trusted link between global manufacturers and local markets.",
        "knowsAbout": [
          "Irrigation Products",
          "PVC Waste & Drainage Systems",
          "Motorbike & Vehicle Parts",
          "Electrical Accessories",
          "G.I Fittings & Valves",
          "PPR Fittings & Pipes"
        ],
        "values": [
          {
            "@type": "DefinedTerm",
            "name": "Quality",
            "description": "We prioritize high-quality materials and manufacturing processes."
          },
          {
            "@type": "DefinedTerm",
            "name": "Innovation",
            "description": "We continuously innovate to improve our products and services."
          },
          {
            "@type": "DefinedTerm",
            "name": "Reliability",
            "description": "Our products are built to last and perform consistently."
          }
        ]
      }
    },
    {
      "@type": "ContactPage",
      "@id": "https://elidaindustries.yellowpageskenya.com/#contact",
      "url": "https://elidaindustries.yellowpageskenya.com/#contact",
      "name": "Contact Elida Industries",
      "description": "Contact information for Elida Industries including physical location, shop location, phone numbers, email, and operating hours.",
      "isPartOf": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "ContactPoint",
        "telephone": "+254716950588",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English",
        "areaServed": "KE",
        "hoursAvailable": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday"
          ],
          "opens": "08:00",
          "closes": "17:00"
        }
      }
    },
    {
      "@type": "CollectionPage",
      "@id": "https://elidaindustries.yellowpageskenya.com/#products",
      "url": "https://elidaindustries.yellowpageskenya.com/#products",
      "name": "Elida Industries Products",
      "description": "Browse our wide range of products including irrigation systems, PVC waste & drainage systems, motorbike parts, electrical accessories, G.I fittings & valves, and PPR fittings & pipes.",
      "isPartOf": {
        "@id": "https://elidaindustries.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Irrigation Products",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "PVC Waste & Drainage Systems",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Motorbike & Vehicle Parts",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Electrical Accessories",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 5,
            "name": "G.I Fittings & Valves",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 6,
            "name": "PPR Fittings & Pipes",
            "url": "https://elidaindustries.yellowpageskenya.com/#products"
          }
        ]
      }
    },
    {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://elidaindustries.yellowpageskenya.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "About Us",
          "item": "https://elidaindustries.yellowpageskenya.com/#about"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Products",
          "item": "https://elidaindustries.yellowpageskenya.com/#products"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "Contact",
          "item": "https://elidaindustries.yellowpageskenya.com/#contact"
        }
      ]
    }
  ]
}
</script>


</head>

<body>

   <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="116" height="70">
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">About Us</a>
                <a href="#products">Products</a>
                <a href="#catalogue">Catalogue</a>
            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="#contact" class="contact-btn">Get In Touch</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="116" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                <a href="#about">About Us</a>
                <a href="#products">Products</a>
                <a href="#catalogue">Catalogue</a>
                <a href="#contact" class="contact-btn">Get In Touch</a>
            </div>
        </div>
    </div>



<section class="hero-slider">

  <!-- Slide 3 -->
  <div class="slide">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_3_scale,w_680.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_3_scale,w_996.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_3_scale,w_1400.webp">
        <img
          src="./img/slider_3_scale,w_1400.webp"
          alt="Prefab Structures Construction"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          fetchpriority="high">
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Elida E.A Industries</span>
        <div class="ht">We live for quality products</div>
        <p>Elida E.A Industries provides products built to perform and made to last. We focus on dependable quality that supports real work across farming, transport and construction.</p>
       
      <a  href="#products" class="explore-btn">View Products</a>
      </div>
    </div>
  </div>

  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_557.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_807.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_1400.webp">
        <img
          src="./img/slider_1a_scale,w_1400.webp"
          alt="slider 8"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          >
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Elida E.A Industries</span>
        <h1>Reliable Products for Farming, Transport & Construction</h1>
        <p>We supply durable, high-quality parts that keep East Africa growing, moving and building with expert support and dependable service.</p>
      <a  href="#products" class="explore-btn">View Products</a>
      </div>
    </div>
  </div>

  <!-- Slide 2 -->
  <div class="slide">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_4_scale,w_536.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_4_scale,w_913.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_4_scale,w_1400.webp">
        <img
          src="./img/slider_4_scale,w_1400.webp"
          alt="Manufacturing EPS 3D Panels"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async">
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Elida E.A Industries </span>
        <div class="ht">Reliable Supply of High-Quality Products, Every Time</div>
        <p>From drip systems to motorcycle parts, Elida provides the essentials that move you forward.</p>
        <a  href="#products" class="explore-btn">View Products</a>
      </div>
    </div>
  </div>



  <!-- Slider controls -->
  <div class="slider-controls">
    <div class="slider-dot active" data-index="0"></div>
    <div class="slider-dot" data-index="1"></div>
    <div class="slider-dot" data-index="2"></div>
  </div>

  <!-- Slider arrows -->
  <div class="slider-arrows">
    <div class="arrow prev">
      <svg viewBox="0 0 24 24">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path>
      </svg>
    </div>
    <div class="arrow next">
      <svg viewBox="0 0 24 24">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path>
      </svg>
    </div>
  </div>
</section>


 <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/about_image_scale,w_427.webp" 
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp"
    srcset="./img/about_image_scale,w_635.webp"
  >
  <img
    src="./img/about_image_scale,w_635.webp"
    alt="Elida about image"
    loading="lazy"
    style="width: 100%; height: 100%;"
  >
</picture>

  </div>

  <!-- <div class="image-container bottom-right-image">
    <picture>
      <source
        srcset="
          ./img/about_image-sm_200.webp 200w,
          ./img/about_image-sm_265.webp 265w"
        sizes="(max-width: 265px) 100vw, 265px"
      >
      <img
        src="./img/about_image-sm_265.webp"
        alt="About image"
        loading="lazy"
        style="width: 100%; height: 100%;"
      >
    </picture>
  </div> -->
</div>

                <div class="about-text">
                    <span class="abt">About Us</span>

                    <p>At Elida E.A Industries, we are committed to keeping you growing, moving, and building. We specialize in the manufacturing of high-quality plastic irrigation systems, motorcycle parts, and construction materials across East Africa.</p>
                    
                    <p>
                    From drip irrigation accessories and PVC gutter pipes to motorcycle accessories, electric fence posts, and fire extinguisher safety seals, we provide dependable products that support agriculture, infrastructure, and mobility.

                    </p>

                    <p>
           Whether you're farming, riding, or constructing, partner with us for the tools and parts that drive your progress. Contact us today and we’ll get you sorted.
                    </p>

             <div class="about-services-container">
                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                Irrigation Products

                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                    PVC Waste & Drainage Systems

                            </li>

                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                         Motorbike & Vehicle Parts
                            </li>

                          
                            

                        </ul>

                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                         Electrical Accessories
                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
          G.I Fittings & Valves
                            </li>

                              <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
   PPR Fittings & Pipes
                            </li>

                          
                            
                        </ul>
                    </div>

                    
                </div>
            </div>
        </div>
    </div>


      <section class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        <!-- <div class="text-content">
          <h2 class="animate-text">Core Values</h2>
          <p class="animate-text-delay">At Elida E.A Industries, our values are the foundation of everything we do. They guide our decisions, shape our culture, and drive our commitment to our customers and partners.</p>
        </div> -->
        <div class="cards-container">
        
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
              </div>
              <h3>Mission</h3>
            </div>
            <p>Committed to supplying high-quality materials that support farming, transport, and construction development.
 
</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                </svg>
              </div>
              <h3>Vision</h3>
            </div>
            <p>To be the trusted link between global manufacturers and local markets.

</p>
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3" />
                </svg>
              </div>
              <h3>Core Values</h3>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Quality  
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Innovation 
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg> Reliability  
 </span> 
              </li>
                
                


              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </section>

    <div class="content-grid" id="products">
        <div class="services-header">
            <h2 class="products-section-title">Our Products</h2>
        </div>
        <div class="services-grid">
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Irrigation_Products3.webp" alt="Irrigation Products" title="Irrigation Products" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Irrigation Products
</h3>

<p>Provides connectors, take-offs, end caps, rubbers, and drippers for drip and overhead irrigation. Easy to assemble and designed to deliver precise water flow. Great for farms, gardens, and greenhouse systems. </p>
                    
                   
                </div>
              
            </div>
              <div class="service-item">
                <div class="service-image">
                    <img src="./img/PVC_Waste_Drainage_Systems2.webp" alt="PVC Waste & Drainage Systems" title="PVC Waste & Drainage Systems" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>PVC Waste & Drainage Systems
</h3>
<p>Offers floor traps, bends, inspection tees, reducers, and plugs for effective waste and drainage management. Made from high-quality PVC for long-lasting performance. Suitable for both residential and commercial plumbing installations.</p>
                    
                    
                </div>
                
            </div>
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Motorbike_Vehicle_Parts.webp" alt="Motorbike & Vehicle Parts" title="Motorbike & Vehicle Parts" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Motorbike & Vehicle Parts
</h3>

<p>Includes mudguards, lamp holders, visors, cowls, reflectors, and engine oils. These parts enhance safety, performance, and aesthetics. Compatible with popular motorbike and vehicle models. </p>
                    
                  
                </div>
                
            </div>

          
         
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Electrical_Accessories.webp" alt="Electrical Accessories" title="Electrical Accessories" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Electrical Accessories
</h3>

<p>Features patress boxes, conduits, bends, couplers, cable ties, and wall plugs. Designed for safe and efficient electrical wiring. Ideal for new installations and upgrades. </p>
                   
                   
                </div>
                
            </div>

               <div class="service-item">
                <div class="service-image">
                    <img src="./img/Gi_Fittings_Valves.webp" alt="G.I Fittings & Valves" title="G.I Fittings & Valves" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>G.I Fittings & Valves</h3>
                    <p>Comprises galvanized iron valves, tees, elbows, plugs, and taps for heavy-duty plumbing. Built to handle high pressure and resist corrosion. Commonly used in industrial and outdoor plumbing systems.</p>
                    
                
                </div>
                
            </div>


            <div class="service-item">
                <div class="service-image">
                    <img src="./img/PPR_Fittings_Pipes.webp" alt="Shipping Container Insulation" title="Shipping Container Insulation" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>PPR Fittings & Pipes</h3>
                    <p>Includes sockets, elbows, tees, valves, and pipes for hot and cold water plumbing. These are durable, leak-proof, and ideal for both domestic and industrial use. Available in various sizes and designs to suit all plumbing needs.</p>
                    
                </div>
               
            </div>

             

              

        </div>
    </div>

      <section class="parallax-cta lazy-background" data-bg="./img/cta-bg.webp" id="catalogue">

        <div class="content-grid">
          <div class="cta-container">
              <span class="cta-tag">Product Catalogue</span>
              <h2 class="cta-headline">Discover our full product range</h2>
              <p class="cta-subheadline">Get our complete product catalogue for irrigation, construction, and motorcycle parts across East Africa.</p>
              <div class="cta-buttons">
                  <a href="#" class="cta-button cta-button-primary">View Catalogue</a>
                  <!-- <a href="#contact" class="cta-button cta-button-secondary">Contact Us</a> -->
              </div>
          </div>
        </div>
    </section>




  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contact Us</h2>
      <div class="contact-content">
        <div class="contact-map">

          <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d7977.475725029984!2d36.882468!3d-1.333444!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f13831e4b2677%3A0xb232b50c38dc46c6!2sElida%20(East%20Africa)%20Industries%20Limited!5e0!3m2!1sen!2ske!4v1756110025950!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="Elida Industries Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Physical Location</h3>
              <p>Along Old Mombasa Road, Near Cabanas, Next to Solargen, Excel Chemicals, Directly Opposite Kenya Sweets
</p>
              
            </div>
          </div>

           <!-- <div class="contact-item">
            <span>
             

<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="contact-icon"><path fill="currentColor" fill-rule="evenodd" d="M18 14s4.5-5.56 4.5-8.5C22.5 3.18 20.32 1 18 1s-4.5 2.18-4.5 4.5C13.5 8.44 18 14 18 14M1 3h11.606A5.6 5.6 0 0 0 12 5.5c0 .491.084.998.218 1.5H4v2h8.982c.44.922.98 1.842 1.505 2.66c.545.854 1.089 1.62 1.513 2.194V23H9.5v-4h-2v4H1zm3 10.5h9v-2H4zm13-7v-2h2v2z" clip-rule="evenodd"/></svg>


            </span>
            <div>
              <h3>Shop Location</h3>
              <p>Good Hope Building, Junction of Luthuli Avenue and River Road,Nairobi.
</p>
              
            </div>
          </div> -->

          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Phone number</h3>
              <p><a href="tel:+254716950588">+254716950588
</a></p>

          
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Operating Hours</h3>
              <p>Monday to Friday: 8:00am – 5:00pm 
</p>
            </div>
          </div>
          <!-- Social Media Icons -->
          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Connect With Us</h3>


              <div class="social-links">
                <!-- whatsapp -->
               <a href="https://api.whatsapp.com/send/?phone=254716950588&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">

                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>
 <!-- Instagram -->

                <a href="https://www.instagram.com/elidaindustries" target="_blank" class="social-link" aria-label="Instagram">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M16 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M12 7.25a4.75 4.75 0 1 0 0 9.5a4.75 4.75 0 0 0 0-9.5M8.75 12a3.25 3.25 0 1 1 6.5 0a3.25 3.25 0 0 1-6.5 0" clip-rule="evenodd"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M17.258 2.833a47.7 47.7 0 0 0-10.516 0c-2.012.225-3.637 1.81-3.873 3.832a46 46 0 0 0 0 10.67c.236 2.022 1.86 3.607 3.873 3.832a47.8 47.8 0 0 0 10.516 0c2.012-.225 3.637-1.81 3.873-3.832a46 46 0 0 0 0-10.67c-.236-2.022-1.86-3.607-3.873-3.832m-10.35 1.49a46.2 46.2 0 0 1 10.184 0c1.33.15 2.395 1.199 2.55 2.517a44.4 44.4 0 0 1 0 10.32a2.89 2.89 0 0 1-2.55 2.516a46.2 46.2 0 0 1-10.184 0a2.89 2.89 0 0 1-2.55-2.516a44.4 44.4 0 0 1 0-10.32a2.89 2.89 0 0 1 2.55-2.516" clip-rule="evenodd"></path>
                  </svg>
                </a>

                 <!-- Facebook -->
                <a href="https://www.facebook.com/p/Elida-East-Africa-Industries-Limited-61569285294566/" target="_blank"
                  class="social-link" aria-label="Facebook">
                
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.5 13.478a5.5 5.5 0 1 0-1.5-.069V9.75H5.75a.75.75 0 0 1 0-1.5H7V7.24c0-.884.262-1.568.722-2.032S8.843 4.5 9.644 4.5c.273 0 .612.04.948.213a.75.75 0 0 1-.685 1.334A.6.6 0 0 0 9.644 6c-.493 0-.737.144-.857.265c-.12.12-.287.39-.287.975v1.01h1.25a.75.75 0 0 1 0 1.5H8.5zM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14" clip-rule="evenodd"/></svg>
                </a>

              
                <!-- Linked in -->
                <a href="https://www.linkedin.com/company/elida-east-africa-industries-limited/" target="_blank"
                  class="social-link" aria-label="LinkedIn">
               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="-2 -2 24 24"><g fill="currentColor"><path d="M15 11.13v3.697h-2.143v-3.45c0-.866-.31-1.457-1.086-1.457c-.592 0-.945.398-1.1.784c-.056.138-.071.33-.071.522v3.601H8.456s.029-5.842 0-6.447H10.6v.913l-.014.021h.014v-.02c.285-.44.793-1.066 1.932-1.066c1.41 0 2.468.922 2.468 2.902M6.213 5.271C5.48 5.271 5 5.753 5 6.385c0 .62.466 1.115 1.185 1.115h.014c.748 0 1.213-.496 1.213-1.115c-.014-.632-.465-1.114-1.199-1.114m-1.086 9.556h2.144V8.38H5.127z"/><path d="M4 2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm0-2h12a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4"/></g></svg>
                </a>


                
              
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

<footer class="footer">
    <div class="content-grid">
        <div class="footer-content">
            <!-- Elida Industries Limited section on the left -->
            <div class="company-section">
                <img src="./img/logo.webp" loading="lazy" alt="Elida Industries Limited" width="83" height="50" title="Elida Industries Limited">
                <p>&copy; <span id="current-year">2025</span> Elida Industries Limited. All Rights Reserved.</p>
            </div>
            
            <!-- YP logo and text on the right -->
            <div class="designer">
                <a href="https://www.yellowpageskenya.com/" target="_blank" rel="noopener noreferrer">
                    <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50" title="Yellow Pages Kenya">
                    <p>Powered by Yellow Pages Kenya.</p>
                </a>
            </div>
        </div>
    </div>
</footer>

<!-- WhatsApp Floating Button HTML -->
<a href="https://wa.me/254716950588" class="whatsapp-float" target="_blank" rel="noopener noreferrer">
    <svg viewBox="0 0 24 24">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.687"/>
    </svg>
</a>

  <!-- <script src="./js/testimonial.js"></script> -->
  <!-- <script src="./js/main.js"></script> -->

  <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script>



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
 <script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Get all slides
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slider-dot');
        const prevArrow = document.querySelector('.arrow.prev');
        const nextArrow = document.querySelector('.arrow.next');
        let currentSlide = 0;
        let slideInterval;
        let isAnimating = false;
        
        // Function to change slide
        function goToSlide(index) {
          if (isAnimating || currentSlide === index) return;
          
          isAnimating = true;
          
          // Remove active class from all slides and dots
          slides.forEach(slide => slide.classList.remove('active'));
          dots.forEach(dot => dot.classList.remove('active'));
          
          // Add active class to current slide and dot
          slides[index].classList.add('active');
          dots[index].classList.add('active');
          
          // Update current slide index
          currentSlide = index;
          
          // Set a timeout to prevent rapid clicking during transition
          setTimeout(() => {
            isAnimating = false;
          }, 1200); // Match this to your CSS transition time
        }
        
        // Function to go to next slide
        function nextSlide() {
          if (isAnimating) return;
          
          let nextIndex = currentSlide + 1;
          if (nextIndex >= slides.length) {
            nextIndex = 0;
          }
          goToSlide(nextIndex);
        }
        
        // Function to go to previous slide
        function prevSlide() {
          if (isAnimating) return;
          
          let prevIndex = currentSlide - 1;
          if (prevIndex < 0) {
            prevIndex = slides.length - 1;
          }
          goToSlide(prevIndex);
        }
        
        // Set up dot click events
        dots.forEach(dot => {
          dot.addEventListener('click', function() {
            if (isAnimating) return;
            goToSlide(parseInt(this.getAttribute('data-index')));
            restartSlideInterval();
          });
        });
        
        // Set up arrow click events
        nextArrow.addEventListener('click', function() {
          nextSlide();
          restartSlideInterval();
        });
        
        prevArrow.addEventListener('click', function() {
          prevSlide();
          restartSlideInterval();
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowLeft') {
            prevSlide();
            restartSlideInterval();
          } else if (e.key === 'ArrowRight') {
            nextSlide();
            restartSlideInterval();
          }
        });
        
        // Function to start automatic slide transition
        function startSlideInterval() {
          slideInterval = setInterval(nextSlide, 7000); // Change slide every 7 seconds
        }
        
        // Function to restart slide interval after user interaction
        function restartSlideInterval() {
          clearInterval(slideInterval);
          startSlideInterval();
        }
        
        // Pause autoplay on hover
        const sliderContainer = document.querySelector('.hero-slider');
        sliderContainer.addEventListener('mouseenter', function() {
          clearInterval(slideInterval);
        });
        
        sliderContainer.addEventListener('mouseleave', function() {
          startSlideInterval();
        });
        
        // Start automatic slide transition
        startSlideInterval();
      });
    </script> 


</body>

</html>